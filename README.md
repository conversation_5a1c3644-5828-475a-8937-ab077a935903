# Twitter Auth App

A modern authentication application built with Bun, Elysia, and Twitter OAuth. This app demonstrates a clean layered architecture with secure session management and SQLite database integration.

## Features

- 🔐 **Twitter OAuth Authentication** - Secure login using Twitter OAuth 1.0a
- 🏗️ **Layered Architecture** - Clean separation of concerns with controllers, services, and repositories
- 🗄️ **SQLite Database** - Lightweight database with Drizzle ORM
- 🍪 **Session Management** - Secure JWT-based sessions with HTTP-only cookies
- 🎨 **Modern UI** - Responsive design with Tailwind CSS
- ⚡ **Bun Runtime** - Fast JavaScript runtime and package manager

## Project Structure

```
src/
├── config/          # Database and app configuration
├── controllers/     # HTTP request handlers
├── middleware/      # Authentication and other middleware
├── models/          # Database schemas and types
├── repositories/    # Data access layer
├── services/        # Business logic layer
├── types/           # TypeScript type definitions
└── index.tsx        # Main application entry point
```

## Getting Started

### Prerequisites

- [Bun](https://bun.sh/) installed on your system
- Twitter Developer Account for OAuth credentials

### Installation

1. Install dependencies:
```bash
bun install
```

2. Set up environment variables:
   - Update the `.env` file with your Twitter OAuth credentials
   - Get your Twitter OAuth credentials from [Twitter Developer Portal](https://developer.twitter.com/)

3. Generate and run database migrations:
```bash
bun run db:generate
bun run db:migrate
```

4. Start the development server:
```bash
bun run dev
```

The application will be available at `http://localhost:3000`.

## Environment Variables

Update the `.env` file with your actual credentials:

```env
# Database
DATABASE_URL="./database.db"

# Twitter OAuth Configuration
TWITTER_CONSUMER_KEY="your_actual_twitter_consumer_key"
TWITTER_CONSUMER_SECRET="your_actual_twitter_consumer_secret"
TWITTER_ACCESS_TOKEN="your_actual_twitter_access_token"
TWITTER_ACCESS_TOKEN_SECRET="your_actual_twitter_access_token_secret"

# Application Configuration
APP_URL="http://localhost:3000"
JWT_SECRET="your_secure_jwt_secret_here"
SESSION_SECRET="your_secure_session_secret_here"

# Server Configuration
PORT=3000
NODE_ENV="development"
```

## Available Scripts

- `bun run dev` - Start development server with hot reload
- `bun run db:generate` - Generate database migrations
- `bun run db:migrate` - Apply database migrations
- `bun run db:studio` - Open Drizzle Studio for database management
- `bun run format` - Format code with Biome

## API Endpoints

### Authentication Routes
- `GET /` - Home page (redirects to dashboard if authenticated)
- `GET /auth/login` - Login page
- `GET /auth/twitter` - Initiate Twitter OAuth flow
- `GET /auth/twitter/callback` - Twitter OAuth callback
- `POST /auth/logout` - Logout and clear session

### Protected Routes
- `GET /dashboard` - User dashboard (requires authentication)

## Architecture

### Controllers
Handle HTTP requests and responses, delegating business logic to services.

### Services
Contain business logic and coordinate between repositories and external APIs.

### Repositories
Handle data access and database operations using Drizzle ORM.

### Middleware
- **Authentication Middleware**: Validates JWT tokens and manages user sessions
- **CORS**: Handles cross-origin requests
- **Cookie**: Manages secure HTTP-only cookies

## Database Schema

### Users Table
- `id` - Primary key
- `twitterId` - Twitter user ID (unique)
- `username` - Twitter username
- `displayName` - User's display name
- `profileImageUrl` - Profile image URL
- `email` - User's email (optional)
- `createdAt` - Account creation timestamp
- `updatedAt` - Last update timestamp

### Sessions Table
- `id` - Session ID (UUID)
- `userId` - Foreign key to users table
- `expiresAt` - Session expiration timestamp
- `createdAt` - Session creation timestamp

## Security Features

- JWT tokens for stateless authentication
- HTTP-only cookies to prevent XSS attacks
- Secure session management with expiration
- CSRF protection through SameSite cookies
- Environment-based configuration for secrets

## Development Notes

- The Twitter OAuth implementation uses mock data for development
- Replace the mock OAuth flow with actual Twitter API calls for production
- Ensure all environment variables are properly set before deployment
- The app uses Bun's built-in SQLite for better compatibility

## License

This project is licensed under the MIT License.
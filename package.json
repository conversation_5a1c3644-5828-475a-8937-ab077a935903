{"name": "learn-elysia", "version": "0.0.0", "scripts": {"dev": "bun run --watch src/index.tsx", "format": "biome format --write", "db:generate": "bunx drizzle-kit generate", "db:migrate": "bunx drizzle-kit migrate", "db:studio": "bunx drizzle-kit studio"}, "dependencies": {"@elysiajs/cookie": "^0.8.0", "@elysiajs/cors": "^1.3.3", "@elysiajs/html": "^1.3.1", "@elysiajs/jwt": "^1.3.2", "@kitajs/ts-html-plugin": "^4.1.2", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.8.3", "elysia": "latest", "oauth-1.0a": "^2.2.6", "zod": "^4.0.17"}, "devDependencies": {"@biomejs/biome": "2.1.4", "bun-types": "latest"}}
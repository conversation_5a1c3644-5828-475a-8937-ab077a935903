import { Elysia, t } from 'elysia';
import { AuthService } from '../services/authService';

export const authController = new Elysia({ prefix: '/auth' })
  .decorate('authService', new AuthService())
  .get('/login', ({ html }) => {
    return html(`
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Login - Twitter Auth App</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen flex items-center justify-center">
          <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            <div class="text-center mb-8">
              <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome</h1>
              <p class="text-gray-600">Sign in to continue to your dashboard</p>
            </div>

            <div class="space-y-4">
              <a href="/auth/twitter"
                 class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
                Continue with Twitter
              </a>
            </div>

            <div class="mt-6 text-center">
              <p class="text-sm text-gray-500">
                By signing in, you agree to our terms of service and privacy policy.
              </p>
            </div>
          </div>
        </body>
      </html>
    `);
  })

  .get('/twitter', ({ authService, cookie, redirect }) => {
    try {
      const { url, oauth_token, oauth_token_secret } = authService.getRequestToken();

      // Store oauth_token_secret in a secure cookie for the callback
      cookie.oauth_token_secret.set({
        value: oauth_token_secret,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 600, // 10 minutes
      });

      return redirect(url);
    } catch (error) {
      console.error('Twitter OAuth error:', error);
      return redirect('/auth/login?error=oauth_failed');
    }
  })

  .get('/twitter/callback', async ({
    authService,
    query,
    cookie,
    redirect,
    jwt
  }) => {
    try {
      const { oauth_token, oauth_verifier } = query;
      const oauth_token_secret = cookie.oauth_token_secret.value;

      if (!oauth_token || !oauth_verifier || !oauth_token_secret) {
        return redirect('/auth/login?error=missing_params');
      }

      // Clear the temporary cookie
      cookie.oauth_token_secret.remove();

      const user = await authService.handleCallback(
        oauth_token as string,
        oauth_verifier as string,
        oauth_token_secret
      );

      const sessionData = await authService.createSession(user.id);

      // Create JWT token
      const token = await jwt.sign({
        userId: user.id,
        sessionId: sessionData.sessionId,
      });

      // Set session cookie
      cookie.session.set({
        value: token,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60, // 7 days
      });

      return redirect('/dashboard');
    } catch (error) {
      console.error('Twitter callback error:', error);
      return redirect('/auth/login?error=callback_failed');
    }
  }, {
    query: t.Object({
      oauth_token: t.Optional(t.String()),
      oauth_verifier: t.Optional(t.String()),
    })
  })

  .post('/logout', async ({ authService, cookie, redirect, jwt }) => {
    try {
      const token = cookie.session.value;

      if (token) {
        const payload = await jwt.verify(token);
        if (payload && typeof payload === 'object' && 'sessionId' in payload) {
          await authService.logout(payload.sessionId as string);
        }
      }

      cookie.session.remove();
      return redirect('/auth/login?message=logged_out');
    } catch (error) {
      console.error('Logout error:', error);
      cookie.session.remove();
      return redirect('/auth/login');
    }
  });

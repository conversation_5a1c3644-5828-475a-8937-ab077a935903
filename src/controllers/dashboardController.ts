import { Elysia } from 'elysia';
import type { AuthenticatedUser } from '../types/auth';

export const dashboardController = new Elysia()
  .get('/dashboard', ({ html, user }) => {
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }
    return html(`
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Dashboard - Twitter Auth App</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 min-h-screen">
          <!-- Navigation -->
          <nav class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div class="flex justify-between h-16">
                <div class="flex items-center">
                  <h1 class="text-xl font-semibold text-gray-900">Twitter Auth App</h1>
                </div>
                <div class="flex items-center space-x-4">
                  <div class="flex items-center space-x-3">
                    ${user.profileImageUrl ? `
                      <img class="h-8 w-8 rounded-full" src="${user.profileImageUrl}" alt="${user.displayName}">
                    ` : `
                      <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">${user.displayName.charAt(0).toUpperCase()}</span>
                      </div>
                    `}
                    <span class="text-sm font-medium text-gray-700">${user.displayName}</span>
                  </div>
                  <form action="/auth/logout" method="POST" class="inline">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200">
                      Logout
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </nav>

          <!-- Main Content -->
          <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
              <!-- Hero Section -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                  <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                      <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Welcome back, ${user.displayName}!</h2>
                    <p class="text-lg text-gray-600 mb-6">
                      You have successfully authenticated with Twitter. Your dashboard is ready to use.
                    </p>

                    <!-- User Info Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
                      <div class="bg-blue-50 p-6 rounded-lg">
                        <div class="flex items-center">
                          <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                          </div>
                          <div class="ml-4 text-left">
                            <p class="text-sm font-medium text-blue-600">Username</p>
                            <p class="text-lg font-semibold text-gray-900">@${user.username}</p>
                          </div>
                        </div>
                      </div>

                      <div class="bg-green-50 p-6 rounded-lg">
                        <div class="flex items-center">
                          <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                          </div>
                          <div class="ml-4 text-left">
                            <p class="text-sm font-medium text-green-600">Status</p>
                            <p class="text-lg font-semibold text-gray-900">Authenticated</p>
                          </div>
                        </div>
                      </div>

                      <div class="bg-purple-50 p-6 rounded-lg">
                        <div class="flex items-center">
                          <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                          </div>
                          <div class="ml-4 text-left">
                            <p class="text-sm font-medium text-purple-600">Session</p>
                            <p class="text-lg font-semibold text-gray-900">Active</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-8 flex justify-center space-x-4">
                      <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition duration-200">
                        Explore Features
                      </button>
                      <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-md font-medium transition duration-200">
                        View Profile
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </body>
      </html>
    `);
  });

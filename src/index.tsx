import { html } from '@elysiajs/html';
import { Elysia } from 'elysia';
import { cookie } from '@elysiajs/cookie';
import { jwt } from '@elysiajs/jwt';
import { cors } from '@elysiajs/cors';

// Import controllers
import { authController } from './controllers/authController';
import { dashboardController } from './controllers/dashboardController';
import { homeController } from './controllers/homeController';

// Import middleware
import { authMiddleware } from './middleware/auth';

const app = new Elysia()
  .use(html())
  .use(cors())
  .use(cookie())
  .use(jwt({
    name: 'jwt',
    secret: process.env.JWT_SECRET || 'your-secret-key',
  }))
  .use(authMiddleware)
  .use(homeController)
  .use(authController)
  .use(dashboardController)
  .onError(({ code, error, set }) => {
    console.error('Application error:', error);

    if (code === 'NOT_FOUND') {
      set.status = 404;
      return 'Page not found';
    }

    set.status = 500;
    return 'Internal server error';
  })
  .listen(process.env.PORT || 3000);

console.log(
  `🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`,
);

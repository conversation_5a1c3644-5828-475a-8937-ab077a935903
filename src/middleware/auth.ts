import { Elysia } from 'elysia';
import { AuthService } from '../services/authService';
import type { AuthenticatedUser } from '../types/auth';

export const authMiddleware = new Elysia({ name: 'auth' })
  .decorate('authService', new AuthService())
  .derive(async ({ cookie, jwt, authService, request }) => {
    const token = cookie.session?.value;
    
    if (!token) {
      return { user: null };
    }
    
    try {
      const payload = await jwt.verify(token);
      
      if (!payload || typeof payload !== 'object' || !('sessionId' in payload)) {
        return { user: null };
      }
      
      const user = await authService.validateSession(payload.sessionId as string);
      return { user };
    } catch (error) {
      console.error('Auth middleware error:', error);
      return { user: null };
    }
  })
  .macro(({ onBeforeHandle }) => ({
    requireAuth(enabled: boolean = true) {
      if (!enabled) return;
      
      onBeforeHandle(({ user, redirect, request }) => {
        if (!user) {
          const url = new URL(request.url);
          const returnTo = encodeURIComponent(url.pathname + url.search);
          return redirect(`/auth/login?returnTo=${returnTo}`);
        }
      });
    }
  }));

export type AuthContext = {
  user: AuthenticatedUser | null;
};

import { eq, lt } from 'drizzle-orm';
import { db } from '../config/database';
import { sessions, type Session, type NewSession } from '../models/schema';

export class SessionRepository {
  async create(sessionData: Omit<NewSession, 'createdAt'>): Promise<Session> {
    const result = await db.insert(sessions).values({
      ...sessionData,
      createdAt: new Date(),
    }).returning();
    return result[0];
  }

  async findById(id: string): Promise<Session | undefined> {
    const result = await db.select().from(sessions).where(eq(sessions.id, id)).limit(1);
    return result[0];
  }

  async findByUserId(userId: number): Promise<Session[]> {
    return await db.select().from(sessions).where(eq(sessions.userId, userId));
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(sessions).where(eq(sessions.id, id));
    return result.changes > 0;
  }

  async deleteByUserId(userId: number): Promise<number> {
    const result = await db.delete(sessions).where(eq(sessions.userId, userId));
    return result.changes;
  }

  async deleteExpired(): Promise<number> {
    const now = new Date();
    const result = await db.delete(sessions).where(lt(sessions.expiresAt, now));
    return result.changes;
  }

  async isValid(id: string): Promise<boolean> {
    const session = await this.findById(id);
    if (!session) return false;
    return session.expiresAt > new Date();
  }
}

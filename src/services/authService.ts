import OAuth from 'oauth-1.0a';
import crypto from 'crypto';
import { UserRepository } from '../repositories/userRepository';
import { SessionRepository } from '../repositories/sessionRepository';
import type { TwitterUser, AuthenticatedUser, SessionData } from '../types/auth';

export class AuthService {
  private oauth: OAuth;
  private userRepository: UserRepository;
  private sessionRepository: SessionRepository;

  constructor() {
    this.oauth = new OAuth({
      consumer: {
        key: process.env.TWITTER_CONSUMER_KEY!,
        secret: process.env.TWITTER_CONSUMER_SECRET!,
      },
      signature_method: 'HMAC-SHA1',
      hash_function(base_string, key) {
        return crypto.createHmac('sha1', key).update(base_string).digest('base64');
      },
    });

    this.userRepository = new UserRepository();
    this.sessionRepository = new SessionRepository();
  }

  getRequestToken(): { url: string; oauth_token: string; oauth_token_secret: string } {
    const requestData = {
      url: 'https://api.twitter.com/oauth/request_token',
      method: 'POST',
      data: {
        oauth_callback: `${process.env.APP_URL}/auth/twitter/callback`,
      },
    };

    // This is a simplified version - in a real app, you'd make the actual HTTP request
    // For now, we'll return mock data that you can replace with actual Twitter API calls
    const oauth_token = 'mock_oauth_token';
    const oauth_token_secret = 'mock_oauth_token_secret';
    
    return {
      url: `https://api.twitter.com/oauth/authorize?oauth_token=${oauth_token}`,
      oauth_token,
      oauth_token_secret,
    };
  }

  async handleCallback(oauth_token: string, oauth_verifier: string, oauth_token_secret: string): Promise<AuthenticatedUser> {
    // In a real implementation, you would:
    // 1. Exchange the oauth_token and oauth_verifier for an access token
    // 2. Use the access token to fetch user data from Twitter API
    
    // For now, we'll simulate this with mock data
    const twitterUser: TwitterUser = {
      id: 'mock_twitter_id_' + Date.now(),
      username: 'mockuser',
      name: 'Mock User',
      profile_image_url: 'https://via.placeholder.com/150',
      email: '<EMAIL>',
    };

    return await this.findOrCreateUser(twitterUser);
  }

  async findOrCreateUser(twitterUser: TwitterUser): Promise<AuthenticatedUser> {
    let user = await this.userRepository.findByTwitterId(twitterUser.id);

    if (!user) {
      user = await this.userRepository.create({
        twitterId: twitterUser.id,
        username: twitterUser.username,
        displayName: twitterUser.name,
        profileImageUrl: twitterUser.profile_image_url,
        email: twitterUser.email,
      });
    }

    return {
      id: user.id,
      twitterId: user.twitterId,
      username: user.username,
      displayName: user.displayName,
      profileImageUrl: user.profileImageUrl || undefined,
      email: user.email || undefined,
    };
  }

  async createSession(userId: number): Promise<SessionData> {
    const sessionId = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    const session = await this.sessionRepository.create({
      id: sessionId,
      userId,
      expiresAt,
    });

    return {
      userId: session.userId,
      sessionId: session.id,
      expiresAt: session.expiresAt,
    };
  }

  async validateSession(sessionId: string): Promise<AuthenticatedUser | null> {
    const session = await this.sessionRepository.findById(sessionId);
    if (!session || session.expiresAt < new Date()) {
      return null;
    }

    const user = await this.userRepository.findById(session.userId);
    if (!user) {
      return null;
    }

    return {
      id: user.id,
      twitterId: user.twitterId,
      username: user.username,
      displayName: user.displayName,
      profileImageUrl: user.profileImageUrl || undefined,
      email: user.email || undefined,
    };
  }

  async logout(sessionId: string): Promise<boolean> {
    return await this.sessionRepository.delete(sessionId);
  }

  async cleanupExpiredSessions(): Promise<number> {
    return await this.sessionRepository.deleteExpired();
  }
}
